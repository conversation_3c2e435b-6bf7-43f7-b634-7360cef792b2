# CareerWorx Push Notifications Setup Guide

This guide explains how to set up push notifications for the CareerWorx application using Firebase Cloud Messaging (FCM) API V1.

## Overview

The push notification system consists of:
1. **Client-side FCM integration** - Handles receiving and displaying notifications
2. **Notification trigger service** - Queues notifications when events occur
3. **Firebase Cloud Functions** - Processes the notification queue and sends FCM messages
4. **FCM token management** - Manages device tokens for users

## Features Implemented

### Notification Types
1. **Job Application Notifications** (for companies)
   - Triggered when a student applies for a job
   - Includes applicant name, job title, and application ID

2. **Application Status Updates** (for students)
   - Triggered when application status changes (accepted, rejected, reviewed, etc.)
   - Includes company name, job title, and new status

3. **New Message Notifications** (for both)
   - Triggered when someone sends a message in chat
   - Includes sender name and message preview

4. **Job Match Notifications** (for students)
   - Triggered when a new job is posted that matches student criteria (75%+ match)
   - Includes job title, company name, and match percentage

## Setup Instructions

### 1. Firebase Project Configuration

1. Ensure your Firebase project has Cloud Messaging API (V1) enabled
2. Generate a service account key for server-side FCM sending
3. Add the service account key to your Firebase Functions environment

### 2. Deploy Firebase Cloud Functions

```bash
cd firebase-functions
npm install
firebase deploy --only functions
```

### 3. Android App Configuration

The following components have been added to the Android app:

#### Dependencies Added
- `firebase-messaging-ktx`
- `firebase-functions-ktx`

#### Key Classes
- `CareerWorxFirebaseMessagingService` - Handles incoming FCM messages
- `FCMTokenManager` - Manages FCM token lifecycle
- `NotificationTriggerService` - Triggers notifications for app events
- `PushNotification` models - Data classes for notifications

#### Integration Points
- **Login flows** - FCM tokens are initialized after successful login
- **Job application** - Triggers notification to company
- **Application status updates** - Triggers notification to student
- **Message sending** - Triggers notification to recipient
- **Job posting** - Finds matching students and sends notifications

### 4. Firestore Collections

The system uses these Firestore collections:

#### `fcmTokens`
```javascript
{
  userId: string,
  token: string,
  userType: string, // "student", "company", or "admin"
  deviceId: string,
  isActive: boolean,
  createdAt: timestamp,
  lastUpdated: timestamp
}
```

#### `notificationQueue`
```javascript
{
  recipientId: string,
  title: string,
  body: string,
  type: string, // NotificationType enum
  data: object,
  createdAt: timestamp
}
```

#### `notifications`
```javascript
{
  recipientId: string,
  senderId: string,
  title: string,
  body: string,
  type: string,
  data: object,
  isRead: boolean,
  createdAt: timestamp,
  sentAt: timestamp
}
```

### 5. Notification Channels

The app creates a notification channel with ID `careerworx_notifications` for Android 8.0+.

### 6. Testing

To test the notification system:

1. **Install the app on two devices**
2. **Register as different user types** (student and company)
3. **Trigger notification events:**
   - Apply for a job (should notify company)
   - Update application status (should notify student)
   - Send a message (should notify recipient)
   - Post a job (should notify matching students)

### 7. Security Considerations

- FCM tokens are stored securely in Firestore
- Service account keys should be kept secure
- Notification data is validated before sending
- Inactive tokens are automatically cleaned up

### 8. Monitoring

- Check Firebase Functions logs for notification processing
- Monitor Firestore for notification queue and delivery status
- Use Firebase Analytics to track notification engagement

## Troubleshooting

### Common Issues

1. **Notifications not received**
   - Check if FCM token is properly registered
   - Verify Firebase Functions are deployed and running
   - Check device notification permissions

2. **Invalid FCM tokens**
   - Tokens are automatically marked as inactive when invalid
   - Users need to re-login to refresh tokens

3. **Function deployment errors**
   - Ensure Firebase CLI is updated
   - Check Node.js version compatibility
   - Verify Firebase project permissions

### Debug Steps

1. Check Firebase Functions logs:
   ```bash
   firebase functions:log
   ```

2. Verify FCM token registration in Firestore

3. Test notification queue processing manually

4. Check Android app logs for FCM service messages

## Future Enhancements

- Add notification preferences for users
- Implement notification batching for multiple events
- Add rich notifications with action buttons
- Support for iOS push notifications
- Email fallback for critical notifications
