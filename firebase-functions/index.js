const functions = require('firebase-functions');
const admin = require('firebase-admin');

admin.initializeApp();

const db = admin.firestore();
const messaging = admin.messaging();

// Cloud Function to process notification queue
exports.processNotificationQueue = functions.firestore
  .document('notificationQueue/{notificationId}')
  .onCreate(async (snap, context) => {
    const notification = snap.data();
    const notificationId = context.params.notificationId;
    
    console.log('Processing notification:', notificationId, notification);
    
    try {
      // Get FCM tokens for the recipient
      const tokensSnapshot = await db.collection('fcmTokens')
        .where('userId', '==', notification.recipientId)
        .where('isActive', '==', true)
        .get();
      
      if (tokensSnapshot.empty) {
        console.log('No FCM tokens found for user:', notification.recipientId);
        return;
      }
      
      // Prepare FCM message
      const fcmMessage = {
        notification: {
          title: notification.title,
          body: notification.body,
        },
        data: {
          ...notification.data,
          type: notification.type,
          notificationId: notificationId
        },
        android: {
          priority: 'high',
          notification: {
            icon: 'ic_notification',
            color: '#2196F3',
            sound: 'default',
            channelId: 'careerworx_notifications'
          }
        }
      };
      
      // Send to all tokens
      const sendPromises = [];
      tokensSnapshot.docs.forEach(tokenDoc => {
        const tokenData = tokenDoc.data();
        const message = {
          ...fcmMessage,
          token: tokenData.token
        };
        
        sendPromises.push(
          messaging.send(message)
            .then(response => {
              console.log('Successfully sent message:', response);
              return response;
            })
            .catch(error => {
              console.error('Error sending message:', error);
              
              // If token is invalid, mark it as inactive
              if (error.code === 'messaging/invalid-registration-token' ||
                  error.code === 'messaging/registration-token-not-registered') {
                return db.collection('fcmTokens').doc(tokenDoc.id)
                  .update({ isActive: false });
              }
              throw error;
            })
        );
      });
      
      await Promise.all(sendPromises);
      
      // Save notification to user's notification history
      await db.collection('notifications').add({
        ...notification,
        id: notificationId,
        sentAt: admin.firestore.FieldValue.serverTimestamp()
      });
      
      // Delete from queue
      await snap.ref.delete();
      
      console.log('Notification processed successfully:', notificationId);
      
    } catch (error) {
      console.error('Error processing notification:', error);
      
      // Update notification with error status
      await snap.ref.update({
        error: error.message,
        processedAt: admin.firestore.FieldValue.serverTimestamp()
      });
    }
  });

// Cloud Function to clean up old notifications
exports.cleanupOldNotifications = functions.pubsub
  .schedule('every 24 hours')
  .onRun(async (context) => {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - 30); // 30 days ago
    
    const oldNotificationsQuery = db.collection('notifications')
      .where('createdAt', '<', cutoffDate)
      .limit(500);
    
    const snapshot = await oldNotificationsQuery.get();
    
    if (snapshot.empty) {
      console.log('No old notifications to delete');
      return;
    }
    
    const batch = db.batch();
    snapshot.docs.forEach(doc => {
      batch.delete(doc.ref);
    });
    
    await batch.commit();
    console.log(`Deleted ${snapshot.size} old notifications`);
  });

// Cloud Function to clean up inactive FCM tokens
exports.cleanupInactiveTokens = functions.pubsub
  .schedule('every 7 days')
  .onRun(async (context) => {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - 7); // 7 days ago
    
    const inactiveTokensQuery = db.collection('fcmTokens')
      .where('isActive', '==', false)
      .where('lastUpdated', '<', cutoffDate)
      .limit(500);
    
    const snapshot = await inactiveTokensQuery.get();
    
    if (snapshot.empty) {
      console.log('No inactive tokens to delete');
      return;
    }
    
    const batch = db.batch();
    snapshot.docs.forEach(doc => {
      batch.delete(doc.ref);
    });
    
    await batch.commit();
    console.log(`Deleted ${snapshot.size} inactive FCM tokens`);
  });
