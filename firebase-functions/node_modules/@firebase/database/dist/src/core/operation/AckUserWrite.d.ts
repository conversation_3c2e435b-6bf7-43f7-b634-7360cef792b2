/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import { ImmutableTree } from '../util/ImmutableTree';
import { Path } from '../util/Path';
import { Operation, OperationType } from './Operation';
export declare class AckUserWrite implements Operation {
    /** @inheritDoc */ path: Path;
    /** @inheritDoc */ affectedTree: ImmutableTree<boolean>;
    /** @inheritDoc */ revert: boolean;
    /** @inheritDoc */
    type: OperationType;
    /** @inheritDoc */
    source: import("./Operation").OperationSource;
    /**
     * @param affectedTree - A tree containing true for each affected path. Affected paths can't overlap.
     */
    constructor(
    /** @inheritDoc */ path: Path, 
    /** @inheritDoc */ affectedTree: ImmutableTree<boolean>, 
    /** @inheritDoc */ revert: boolean);
    operationForChild(childName: string): AckUserWrite;
}
