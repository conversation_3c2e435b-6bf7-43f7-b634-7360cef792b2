# OneSignal Push Notifications Setup Guide

This guide explains how to set up OneSignal push notifications for the CareerWorx application.

## Overview

OneSignal provides a comprehensive push notification solution with excellent analytics, targeting capabilities, and cross-platform support. The implementation includes:

- **User Segmentation** - Tag users by type, location, field, etc.
- **Targeted Notifications** - Send notifications to specific user groups
- **Rich Analytics** - Track delivery, opens, and engagement
- **Easy Management** - Excellent dashboard for managing notifications

## Features Implemented

### Notification Types
1. **Job Application Notifications** (for companies)
   - Triggered when a student applies for a job
   - Includes applicant name, job title, and application ID

2. **Application Status Updates** (for students)
   - Triggered when application status changes (accepted, rejected, reviewed, etc.)
   - Includes company name, job title, and new status with emojis

3. **New Message Notifications** (for both)
   - Triggered when someone sends a message in chat
   - Includes sender name and message preview

4. **Job Match Notifications** (for students)
   - Triggered when a new job is posted that matches student criteria
   - Targets students by field, subfield, and location

5. **Interview Scheduled** (for students)
   - Triggered when companies schedule interviews
   - Includes date, time, and job details

## Setup Instructions

### 1. Create OneSignal Account

1. Go to [OneSignal.com](https://onesignal.com) and create a free account
2. Click "New App/Website" and select "Android"
3. Enter your app name: "CareerWorx"
4. Follow the setup wizard

### 2. Configure Android App

1. In OneSignal dashboard, go to Settings > Platforms
2. Click on Android (Google Android)
3. Upload your Firebase Server Key:
   - Go to Firebase Console > Project Settings > Cloud Messaging
   - Copy the Server Key
   - Paste it in OneSignal Android configuration
4. Enter your Firebase Sender ID (Project Number)

### 3. Get OneSignal App ID and REST API Key

1. In OneSignal dashboard, go to Settings > Keys & IDs
2. Copy the **OneSignal App ID**
3. Copy the **REST API Key**

### 4. Update OneSignalConfig.kt

Replace the placeholder values in `OneSignalConfig.kt`:

```kotlin
object OneSignalConfig {
    const val ONESIGNAL_APP_ID = "YOUR_ACTUAL_ONESIGNAL_APP_ID"
    const val ONESIGNAL_REST_API_KEY = "YOUR_ACTUAL_REST_API_KEY"
}
```

### 5. Test Notifications

1. Install the app on a test device
2. Log in as a student or company user
3. Trigger notification events:
   - Apply for a job (should notify company)
   - Update application status (should notify student)
   - Send a message (should notify recipient)
   - Post a job (should notify matching students)

## User Tagging System

The app automatically tags users with relevant information:

- **user_type**: "student" or "company"
- **user_id**: Firebase Auth UID
- **company_id**: Company registration number (for companies)
- **field**: User's field of interest/expertise
- **subfield**: User's specialization
- **city**: User's city
- **province**: User's province

## Notification Targeting

### Job Applications
- Targets specific company user by `user_id`

### Application Status Updates
- Targets specific student user by `user_id`

### New Messages
- Targets specific user by `user_id`

### Job Matches
- Targets students by:
  - `user_type = "student"`
  - `field = job.field` (if specified)
  - `subfield = job.specialization` (if specified)
  - `city = job.city` OR `province = job.province`

## OneSignal Dashboard Features

### Analytics
- Delivery rates
- Open rates
- Click-through rates
- User engagement metrics

### Audience Segmentation
- View users by tags
- Create custom segments
- Export user data

### Message Templates
- Create reusable notification templates
- A/B testing capabilities
- Scheduled notifications

### Automation
- Set up automated notification workflows
- Trigger-based messaging
- User journey automation

## Free Tier Limits

- **Mobile Push**: Unlimited
- **Web Push**: 10,000 subscribers
- **Email**: 10,000 subscribers
- **SMS**: Pay per message

## Best Practices

1. **User Experience**
   - Don't send too many notifications
   - Make notifications relevant and actionable
   - Use emojis to make notifications more engaging

2. **Targeting**
   - Use tags effectively for precise targeting
   - Test notification delivery with small groups first
   - Monitor analytics to optimize performance

3. **Content**
   - Keep titles short and descriptive
   - Include relevant action data
   - Use consistent branding

## Troubleshooting

### Common Issues

1. **Notifications not received**
   - Check OneSignal App ID is correct
   - Verify REST API Key
   - Ensure user has granted notification permissions
   - Check device notification settings

2. **User not tagged properly**
   - Verify login flow calls `setUserTags()`
   - Check OneSignal dashboard for user tags
   - Ensure user is subscribed

3. **Targeting not working**
   - Verify tag names match exactly
   - Check filter logic in notification calls
   - Test with simple filters first

### Debug Steps

1. Check OneSignal dashboard for:
   - User subscription status
   - User tags
   - Notification delivery reports

2. Check app logs for:
   - OneSignal initialization
   - Tag setting success/failure
   - Notification sending attempts

3. Test notifications manually from OneSignal dashboard

## Production Considerations

1. **Remove debug logging** in `CareerWorxApp.kt`
2. **Secure API keys** - Consider using environment variables
3. **Monitor usage** - Track against OneSignal limits
4. **User preferences** - Allow users to control notification types
5. **Compliance** - Ensure GDPR/privacy compliance for user data

## Migration from FCM

If migrating from Firebase Cloud Messaging:
- OneSignal handles FCM tokens automatically
- No need to manage tokens manually
- Better analytics and targeting capabilities
- Easier cross-platform implementation
