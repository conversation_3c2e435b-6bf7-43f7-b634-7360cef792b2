<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.example.jobrec">


    <uses-permission android:name="android.permission.INTERNET" />

    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />

    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <!-- Required for Android 13+ (API 33+) -->
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="com.google.android.gms.permission.AD_ID"/>

    <!-- OneSignal Permissions -->
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />

    <application
        android:name=".CareerWorxApp"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@drawable/ic_launcher_custom"
        android:label="@string/app_name"
        android:roundIcon="@drawable/ic_launcher_custom"
        android:supportsRtl="true"
        android:theme="@style/Theme.CareerWorx"
        tools:targetApi="31">

        <meta-data
            android:name="com.google.android.gms.version"
            android:value="@integer/google_play_services_version" />


        <activity
            android:name=".SplashActivity"
            android:exported="true"
            android:theme="@style/Theme.CareerWorx.Splash">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>


        <activity
            android:name=".LoginActivity"
            android:exported="true"
            android:windowSoftInputMode="adjustResize">
        </activity>

        <activity
            android:name=".SignupActivity"
            android:exported="false"
            android:windowSoftInputMode="adjustResize" />

        <activity
            android:name=".CompanySignupActivity"
            android:exported="false"
            android:windowSoftInputMode="adjustResize" />

        <activity
            android:name=".UnifiedSignupActivity"
            android:exported="false"
            android:windowSoftInputMode="adjustResize" />

        <activity
            android:name=".CandidateSearchActivity"
            android:exported="false"
            android:windowSoftInputMode="adjustResize" />

        <activity
            android:name=".HomeActivity"
            android:exported="true">
        </activity>

        <activity
            android:name=".SearchActivity"
            android:exported="false"
            android:windowSoftInputMode="adjustResize" />

        <activity
            android:name=".ProfileActivity"
            android:exported="false"
            android:windowSoftInputMode="adjustResize" />

        <activity
            android:name=".CompanyDashboardActivity"
            android:exported="false"
            android:windowSoftInputMode="adjustResize" />

        <activity
            android:name=".CompanyDashboardActivityNew"
            android:exported="false"
            android:windowSoftInputMode="adjustResize" />


        <activity
            android:name=".JobDetailsActivity"
            android:exported="false"
            android:windowSoftInputMode="adjustResize" />

        <activity
            android:name=".JobsActivity"
            android:exported="false"
            android:windowSoftInputMode="adjustResize" />

        <activity
            android:name=".PostJobActivity"
            android:exported="false"
            android:windowSoftInputMode="adjustResize" />


        <activity
            android:name=".HelpActivity"
            android:exported="false"
            android:windowSoftInputMode="adjustResize" />

        <activity
            android:name=".ContactActivity"
            android:exported="false"
            android:windowSoftInputMode="adjustResize" />


        <activity
            android:name=".MyApplicationsActivity"
            android:exported="false"
            android:parentActivityName=".HomeActivity" />


        <activity
            android:name=".ApplicationsActivity"
            android:exported="false"
            android:windowSoftInputMode="adjustResize" />


        <activity
            android:name=".CVDetailsActivity"
            android:exported="false"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name=".EditCompanyProfileActivity"
            android:exported="false"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name=".CompanyProfileActivity"
            android:exported="false"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name=".JobDetailActivity"
            android:exported="false"
            android:windowSoftInputMode="adjustResize" />


        <activity
            android:name=".JobAlertsActivity"
            android:exported="false"
            android:windowSoftInputMode="adjustResize" />


        <activity
            android:name=".SavedJobsActivity"
            android:exported="false"
            android:label="Saved Jobs"
            android:parentActivityName=".HomeActivity" />


        <activity
            android:name=".StudentApplicationDetailsActivity"
            android:exported="false"
            android:label="Application Details"
            android:parentActivityName=".MyApplicationsActivity" />

        <activity
            android:name=".CompanyApplicationDetailsActivity"
            android:exported="false"
            android:label="Application Review"
            android:parentActivityName=".ApplicationsActivity" />


        <activity
            android:name=".ViewCvActivity"
            android:exported="false"
            android:windowSoftInputMode="adjustResize" />

        <activity
            android:name=".PdfViewerActivity"
            android:exported="false"
            android:label="PDF Viewer"
            android:windowSoftInputMode="adjustResize" />




        <service
            android:name="com.google.firebase.components.ComponentDiscoveryService"
            android:exported="false">
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.firestore.FirestoreRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.storage.StorageRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
        </service>

        <!-- Firebase Messaging Service -->
        <service
            android:name=".MyFirebaseMessagingService"
            android:exported="false">
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
            </intent-filter>
        </service>

        <activity
            android:name=".AdminLoginActivity"
            android:exported="true"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name=".AdminDashboardActivity"
            android:exported="false"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name=".AdminUsersActivity"
            android:exported="false"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name=".AdminCompaniesActivity"
            android:exported="false"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name=".AdminJobsActivity"
            android:exported="false"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name=".AdminApplicationsActivity"
            android:exported="false"
            android:windowSoftInputMode="adjustResize" />

        <activity
            android:name=".EmployerAnalyticsActivity"
            android:label="Analytics Dashboard"
            android:windowSoftInputMode="adjustResize"
            android:parentActivityName=".CompanyDashboardActivityNew">
            <meta-data
                android:name="android.support.PARENT_ACTIVITY"
                android:value=".CompanyDashboardActivityNew" />
        </activity>

        <activity
            android:name=".EmployerJobsActivity"
            android:exported="false"
            android:windowSoftInputMode="adjustResize" />

        <activity
            android:name=".EditJobActivity"
            android:exported="false"
            android:windowSoftInputMode="adjustResize" />

        <activity
            android:name=".CompanyAnalyticsActivity"
            android:exported="false"
            android:windowSoftInputMode="adjustResize" />


        <activity
            android:name=".ChatActivity"
            android:exported="false"
            android:windowSoftInputMode="adjustResize" />

        <activity
            android:name=".ConversationsActivity"
            android:exported="false"
            android:windowSoftInputMode="adjustResize" />


        <activity
            android:name=".chatbot.ChatbotActivity"
            android:exported="false"
            android:windowSoftInputMode="adjustResize" />




        <!-- FileProvider for sharing temporary PDF files -->
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>

    </application>

</manifest>