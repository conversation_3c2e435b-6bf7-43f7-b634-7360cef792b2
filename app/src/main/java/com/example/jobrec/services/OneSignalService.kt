package com.example.jobrec.services

import android.util.Log
import com.example.jobrec.services.OneSignalConfig.Tags
import com.onesignal.OneSignal
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import okhttp3.*
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.RequestBody.Companion.toRequestBody
import org.json.JSONArray
import org.json.JSONObject
import java.io.IOException

class OneSignalService private constructor() {

    companion object {
        private const val TAG = "OneSignalService"
        private const val ONESIGNAL_API_URL = "https://onesignal.com/api/v1/notifications"

        @Volatile
        private var INSTANCE: OneSignalService? = null

        fun getInstance(): OneSignalService {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: OneSignalService().also { INSTANCE = it }
            }
        }
    }

    private val client = OkHttpClient()

    /**
     * Set user tags when they log in
     */
    fun setUserTags(
        userType: String, // "student" or "company"
        userId: String,
        companyId: String? = null,
        field: String? = null,
        subField: String? = null,
        city: String? = null,
        province: String? = null
    ) {
        try {
            Log.d(TAG, "Setting OneSignal user tags for user: $userId, type: $userType")

            val tags = mutableMapOf<String, String>().apply {
                put(Tags.USER_TYPE, userType)
                put(Tags.USER_ID, userId)
                companyId?.let { put(Tags.COMPANY_ID, it) }
                field?.let { put(Tags.FIELD, it) }
                subField?.let { put(Tags.SUBFIELD, it) }
                city?.let { put(Tags.CITY, it) }
                province?.let { put(Tags.PROVINCE, it) }
            }

            OneSignal.User.addTags(tags)
            Log.d(TAG, "OneSignal tags set successfully: $tags")

            // Also set external user ID for easier targeting
            OneSignal.User.setExternalUserId(userId)
            Log.d(TAG, "OneSignal external user ID set: $userId")

            // Log subscription status
            val isSubscribed = OneSignal.User.pushSubscription.optedIn
            val pushToken = OneSignal.User.pushSubscription.token
            val oneSignalId = OneSignal.User.onesignalId

            Log.d(TAG, "OneSignal User Status:")
            Log.d(TAG, "  - OneSignal ID: $oneSignalId")
            Log.d(TAG, "  - Push Token: $pushToken")
            Log.d(TAG, "  - Subscribed: $isSubscribed")
            Log.d(TAG, "  - External ID: $userId")

        } catch (e: Exception) {
            Log.e(TAG, "Error setting OneSignal tags", e)
        }
    }

    /**
     * Send notification to specific users by tags
     */
    suspend fun sendNotificationToUsers(
        title: String,
        message: String,
        filters: List<Map<String, Any>>,
        data: Map<String, String> = emptyMap(),
        notificationType: String
    ) {
        withContext(Dispatchers.IO) {
            try {
                val notification = JSONObject().apply {
                    put("app_id", OneSignalConfig.ONESIGNAL_APP_ID)
                    put("headings", JSONObject().put("en", title))
                    put("contents", JSONObject().put("en", message))
                    put("filters", JSONArray(filters))

                    // Add custom data
                    val customData = JSONObject().apply {
                        put("type", notificationType)
                        data.forEach { (key, value) ->
                            put(key, value)
                        }
                    }
                    put("data", customData)

                    // Android specific settings
                    put("android_accent_color", "FF2196F3")
                    put("small_icon", "ic_notification")
                    put("large_icon", "ic_launcher")
                }

                val requestBody = notification.toString()
                    .toRequestBody("application/json".toMediaType())

                val request = Request.Builder()
                    .url(ONESIGNAL_API_URL)
                    .post(requestBody)
                    .addHeader("Authorization", "Basic ${OneSignalConfig.ONESIGNAL_REST_API_KEY}")
                    .addHeader("Content-Type", "application/json")
                    .build()

                client.newCall(request).execute().use { response ->
                    if (response.isSuccessful) {
                        Log.d(TAG, "OneSignal notification sent successfully")
                        Log.d(TAG, "Response: ${response.body?.string()}")
                    } else {
                        Log.e(TAG, "Failed to send OneSignal notification: ${response.code}")
                        Log.e(TAG, "Error body: ${response.body?.string()}")
                    }
                }
            } catch (e: IOException) {
                Log.e(TAG, "Network error sending OneSignal notification", e)
            } catch (e: Exception) {
                Log.e(TAG, "Error sending OneSignal notification", e)
            }
        }
    }

    /**
     * Send notification to a specific user by user ID
     */
    suspend fun sendNotificationToUser(
        userId: String,
        title: String,
        message: String,
        data: Map<String, String> = emptyMap(),
        notificationType: String
    ) {
        val filters = listOf(
            mapOf(
                "field" to "tag",
                "key" to Tags.USER_ID,
                "relation" to "=",
                "value" to userId
            )
        )

        sendNotificationToUsers(title, message, filters, data, notificationType)
    }

    /**
     * Send notification to all company users
     */
    suspend fun sendNotificationToCompanies(
        title: String,
        message: String,
        data: Map<String, String> = emptyMap(),
        notificationType: String
    ) {
        val filters = listOf(
            mapOf(
                "field" to "tag",
                "key" to Tags.USER_TYPE,
                "relation" to "=",
                "value" to "company"
            )
        )

        sendNotificationToUsers(title, message, filters, data, notificationType)
    }

    /**
     * Send notification to students in specific field/location
     */
    suspend fun sendNotificationToMatchingStudents(
        title: String,
        message: String,
        field: String? = null,
        subField: String? = null,
        city: String? = null,
        province: String? = null,
        data: Map<String, String> = emptyMap(),
        notificationType: String
    ) {
        val filters = mutableListOf<Map<String, Any>>().apply {
            // Base filter for students
            add(mapOf(
                "field" to "tag",
                "key" to Tags.USER_TYPE,
                "relation" to "=",
                "value" to "student"
            ))

            // Add field filter if specified
            field?.let {
                add(mapOf("operator" to "AND"))
                add(mapOf(
                    "field" to "tag",
                    "key" to Tags.FIELD,
                    "relation" to "=",
                    "value" to it
                ))
            }

            // Add subfield filter if specified
            subField?.let {
                add(mapOf("operator" to "AND"))
                add(mapOf(
                    "field" to "tag",
                    "key" to Tags.SUBFIELD,
                    "relation" to "=",
                    "value" to it
                ))
            }

            // Add location filters if specified
            city?.let {
                add(mapOf("operator" to "AND"))
                add(mapOf(
                    "field" to "tag",
                    "key" to Tags.CITY,
                    "relation" to "=",
                    "value" to it
                ))
            }

            province?.let {
                add(mapOf("operator" to "OR"))
                add(mapOf(
                    "field" to "tag",
                    "key" to Tags.PROVINCE,
                    "relation" to "=",
                    "value" to it
                ))
            }
        }

        sendNotificationToUsers(title, message, filters, data, notificationType)
    }

    /**
     * Clear user tags on logout
     */
    fun clearUserTags() {
        try {
            OneSignal.User.removeTags(listOf(
                Tags.USER_TYPE,
                Tags.USER_ID,
                Tags.COMPANY_ID,
                Tags.FIELD,
                Tags.SUBFIELD,
                Tags.CITY,
                Tags.PROVINCE
            ))
            Log.d(TAG, "OneSignal tags cleared")
        } catch (e: Exception) {
            Log.e(TAG, "Error clearing OneSignal tags", e)
        }
    }
}
