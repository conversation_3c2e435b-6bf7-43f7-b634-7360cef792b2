package com.example.jobrec.services

import android.util.Log
import com.example.jobrec.models.NotificationType
import com.example.jobrec.models.PushNotification
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.functions.FirebaseFunctions
import kotlinx.coroutines.tasks.await

class NotificationTriggerService private constructor() {
    
    companion object {
        private const val TAG = "NotificationTrigger"
        private const val COLLECTION_NOTIFICATION_QUEUE = "notificationQueue"
        
        @Volatile
        private var INSTANCE: NotificationTriggerService? = null
        
        fun getInstance(): NotificationTriggerService {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: NotificationTriggerService().also { INSTANCE = it }
            }
        }
    }
    
    private val db = FirebaseFirestore.getInstance()
    private val functions = FirebaseFunctions.getInstance()
    
    suspend fun triggerJobApplicationNotification(
        companyUserId: String,
        applicantName: String,
        jobTitle: String,
        applicationId: String
    ) {
        try {
            val title = "New Job Application"
            val body = "$applicantName has applied for $jobTitle"
            val data = mapOf(
                "applicationId" to applicationId,
                "jobTitle" to jobTitle,
                "applicantName" to applicantName,
                "action" to "view_application"
            )
            
            queueNotification(
                recipientId = companyUserId,
                title = title,
                body = body,
                type = NotificationType.JOB_APPLICATION,
                data = data
            )
            
        } catch (e: Exception) {
            Log.e(TAG, "Error triggering job application notification", e)
        }
    }
    
    suspend fun triggerApplicationStatusUpdateNotification(
        studentUserId: String,
        companyName: String,
        jobTitle: String,
        status: String,
        applicationId: String
    ) {
        try {
            val title = "Application Update"
            val body = when (status.lowercase()) {
                "accepted" -> "Congratulations! Your application for $jobTitle at $companyName has been accepted"
                "rejected" -> "Your application for $jobTitle at $companyName has been reviewed"
                "reviewed" -> "Your application for $jobTitle at $companyName is being reviewed"
                "shortlisted" -> "Great news! You've been shortlisted for $jobTitle at $companyName"
                "interviewing" -> "You have an interview scheduled for $jobTitle at $companyName"
                else -> "Your application for $jobTitle at $companyName has been updated"
            }
            
            val data = mapOf(
                "applicationId" to applicationId,
                "jobTitle" to jobTitle,
                "companyName" to companyName,
                "status" to status,
                "action" to "view_application_details"
            )
            
            queueNotification(
                recipientId = studentUserId,
                title = title,
                body = body,
                type = NotificationType.APPLICATION_STATUS_UPDATE,
                data = data
            )
            
        } catch (e: Exception) {
            Log.e(TAG, "Error triggering application status update notification", e)
        }
    }
    
    suspend fun triggerNewMessageNotification(
        recipientId: String,
        senderName: String,
        messagePreview: String,
        conversationId: String,
        senderType: String
    ) {
        try {
            val title = "New Message"
            val body = "$senderName: ${messagePreview.take(50)}${if (messagePreview.length > 50) "..." else ""}"
            
            val data = mapOf(
                "conversationId" to conversationId,
                "senderName" to senderName,
                "senderType" to senderType,
                "action" to "open_chat"
            )
            
            queueNotification(
                recipientId = recipientId,
                title = title,
                body = body,
                type = NotificationType.NEW_MESSAGE,
                data = data
            )
            
        } catch (e: Exception) {
            Log.e(TAG, "Error triggering new message notification", e)
        }
    }
    
    suspend fun triggerNewJobMatchNotification(
        studentUserId: String,
        jobTitle: String,
        companyName: String,
        matchPercentage: Int,
        jobId: String
    ) {
        try {
            val title = "New Job Match"
            val body = "New job opportunity: $jobTitle at $companyName (${matchPercentage}% match)"
            
            val data = mapOf(
                "jobId" to jobId,
                "jobTitle" to jobTitle,
                "companyName" to companyName,
                "matchPercentage" to matchPercentage.toString(),
                "action" to "view_job"
            )
            
            queueNotification(
                recipientId = studentUserId,
                title = title,
                body = body,
                type = NotificationType.NEW_JOB_MATCH,
                data = data
            )
            
        } catch (e: Exception) {
            Log.e(TAG, "Error triggering new job match notification", e)
        }
    }
    
    private suspend fun queueNotification(
        recipientId: String,
        title: String,
        body: String,
        type: NotificationType,
        data: Map<String, String>
    ) {
        try {
            val notification = PushNotification(
                recipientId = recipientId,
                title = title,
                body = body,
                type = type,
                data = data
            )
            
            // Add to notification queue - Firebase Functions will process this
            db.collection(COLLECTION_NOTIFICATION_QUEUE)
                .add(notification)
                .await()
            
            Log.d(TAG, "Notification queued for user: $recipientId")
            
        } catch (e: Exception) {
            Log.e(TAG, "Error queueing notification", e)
        }
    }
    
    suspend fun markNotificationAsRead(notificationId: String) {
        try {
            db.collection("notifications")
                .document(notificationId)
                .update("isRead", true)
                .await()
            
            Log.d(TAG, "Notification marked as read: $notificationId")
        } catch (e: Exception) {
            Log.e(TAG, "Error marking notification as read", e)
        }
    }
    
    suspend fun getUserNotifications(userId: String): List<PushNotification> {
        return try {
            val result = db.collection("notifications")
                .whereEqualTo("recipientId", userId)
                .orderBy("createdAt", com.google.firebase.firestore.Query.Direction.DESCENDING)
                .limit(50)
                .get()
                .await()
            
            result.documents.mapNotNull { doc ->
                doc.toObject(PushNotification::class.java)?.copy(id = doc.id)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting user notifications", e)
            emptyList()
        }
    }
}
