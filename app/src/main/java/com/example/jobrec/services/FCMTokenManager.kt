package com.example.jobrec.services

import android.content.Context
import android.provider.Settings
import android.util.Log
import com.example.jobrec.models.FCMToken
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.messaging.FirebaseMessaging
import kotlinx.coroutines.tasks.await

class FCMTokenManager private constructor() {
    
    companion object {
        private const val TAG = "FCMTokenManager"
        private const val COLLECTION_FCM_TOKENS = "fcmTokens"
        
        @Volatile
        private var INSTANCE: FCMTokenManager? = null
        
        fun getInstance(): FCMTokenManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: FCMTokenManager().also { INSTANCE = it }
            }
        }
    }
    
    private val db = FirebaseFirestore.getInstance()
    private val auth = FirebaseAuth.getInstance()
    private val messaging = FirebaseMessaging.getInstance()
    
    suspend fun initializeToken(context: Context, userType: String) {
        try {
            val currentUser = auth.currentUser
            if (currentUser == null) {
                Log.w(TAG, "Cannot initialize FCM token: User not authenticated")
                return
            }
            
            val token = messaging.token.await()
            val deviceId = Settings.Secure.getString(context.contentResolver, Settings.Secure.ANDROID_ID)
            
            Log.d(TAG, "FCM Token retrieved: $token")
            
            val fcmToken = FCMToken(
                userId = currentUser.uid,
                token = token,
                userType = userType,
                deviceId = deviceId,
                isActive = true
            )
            
            saveTokenToFirestore(fcmToken)
            
        } catch (e: Exception) {
            Log.e(TAG, "Error initializing FCM token", e)
        }
    }
    
    suspend fun refreshToken(context: Context, userType: String) {
        try {
            messaging.deleteToken().await()
            initializeToken(context, userType)
        } catch (e: Exception) {
            Log.e(TAG, "Error refreshing FCM token", e)
        }
    }
    
    private suspend fun saveTokenToFirestore(fcmToken: FCMToken) {
        try {
            // Check if token already exists for this user and device
            val existingTokens = db.collection(COLLECTION_FCM_TOKENS)
                .whereEqualTo("userId", fcmToken.userId)
                .whereEqualTo("deviceId", fcmToken.deviceId)
                .get()
                .await()
            
            if (!existingTokens.isEmpty) {
                // Update existing token
                val docId = existingTokens.documents[0].id
                db.collection(COLLECTION_FCM_TOKENS)
                    .document(docId)
                    .set(fcmToken)
                    .await()
                Log.d(TAG, "FCM token updated for user: ${fcmToken.userId}")
            } else {
                // Create new token document
                db.collection(COLLECTION_FCM_TOKENS)
                    .add(fcmToken)
                    .await()
                Log.d(TAG, "FCM token saved for user: ${fcmToken.userId}")
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "Error saving FCM token to Firestore", e)
        }
    }
    
    suspend fun getTokensForUser(userId: String): List<FCMToken> {
        return try {
            val result = db.collection(COLLECTION_FCM_TOKENS)
                .whereEqualTo("userId", userId)
                .whereEqualTo("isActive", true)
                .get()
                .await()
            
            result.documents.mapNotNull { doc ->
                doc.toObject(FCMToken::class.java)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting tokens for user: $userId", e)
            emptyList()
        }
    }
    
    suspend fun deactivateToken(userId: String, deviceId: String) {
        try {
            val tokens = db.collection(COLLECTION_FCM_TOKENS)
                .whereEqualTo("userId", userId)
                .whereEqualTo("deviceId", deviceId)
                .get()
                .await()
            
            tokens.documents.forEach { doc ->
                db.collection(COLLECTION_FCM_TOKENS)
                    .document(doc.id)
                    .update("isActive", false)
                    .await()
            }
            
            Log.d(TAG, "FCM token deactivated for user: $userId, device: $deviceId")
        } catch (e: Exception) {
            Log.e(TAG, "Error deactivating FCM token", e)
        }
    }
    
    suspend fun deactivateAllTokensForUser(userId: String) {
        try {
            val tokens = db.collection(COLLECTION_FCM_TOKENS)
                .whereEqualTo("userId", userId)
                .get()
                .await()
            
            tokens.documents.forEach { doc ->
                db.collection(COLLECTION_FCM_TOKENS)
                    .document(doc.id)
                    .update("isActive", false)
                    .await()
            }
            
            Log.d(TAG, "All FCM tokens deactivated for user: $userId")
        } catch (e: Exception) {
            Log.e(TAG, "Error deactivating all FCM tokens for user", e)
        }
    }
}
