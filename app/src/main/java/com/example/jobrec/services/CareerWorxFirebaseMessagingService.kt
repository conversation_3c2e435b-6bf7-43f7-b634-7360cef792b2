package com.example.jobrec.services

import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.media.RingtoneManager
import android.os.Build
import android.util.Log
import androidx.core.app.NotificationCompat
import com.example.jobrec.R
import com.example.jobrec.SplashActivity
import com.example.jobrec.models.NotificationType
import com.google.firebase.messaging.FirebaseMessagingService
import com.google.firebase.messaging.RemoteMessage
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class CareerWorxFirebaseMessagingService : FirebaseMessagingService() {
    
    companion object {
        private const val TAG = "FCMService"
        private const val CHANNEL_ID = "careerworx_notifications"
        private const val CHANNEL_NAME = "CareerWorx Notifications"
        private const val CHANNEL_DESCRIPTION = "Notifications for job applications, messages, and updates"
    }
    
    override fun onCreate() {
        super.onCreate()
        createNotificationChannel()
    }
    
    override fun onMessageReceived(remoteMessage: RemoteMessage) {
        Log.d(TAG, "From: ${remoteMessage.from}")
        
        // Check if message contains a notification payload
        remoteMessage.notification?.let { notification ->
            Log.d(TAG, "Message Notification Body: ${notification.body}")
            
            val title = notification.title ?: "CareerWorx"
            val body = notification.body ?: ""
            val data = remoteMessage.data
            
            showNotification(title, body, data)
        }
        
        // Check if message contains data payload
        if (remoteMessage.data.isNotEmpty()) {
            Log.d(TAG, "Message data payload: ${remoteMessage.data}")
            
            val title = remoteMessage.data["title"] ?: "CareerWorx"
            val body = remoteMessage.data["body"] ?: ""
            
            showNotification(title, body, remoteMessage.data)
        }
    }
    
    override fun onNewToken(token: String) {
        Log.d(TAG, "Refreshed token: $token")
        
        // Send token to server or update in Firestore
        CoroutineScope(Dispatchers.IO).launch {
            try {
                // The token will be updated when user logs in next time
                // or we can update it here if we have user context
                Log.d(TAG, "New FCM token received, will be updated on next login")
            } catch (e: Exception) {
                Log.e(TAG, "Error handling new token", e)
            }
        }
    }
    
    private fun showNotification(title: String, body: String, data: Map<String, String>) {
        val intent = createNotificationIntent(data)
        val pendingIntent = PendingIntent.getActivity(
            this, 
            0, 
            intent, 
            PendingIntent.FLAG_ONE_SHOT or PendingIntent.FLAG_IMMUTABLE
        )
        
        val defaultSoundUri = RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION)
        
        val notificationBuilder = NotificationCompat.Builder(this, CHANNEL_ID)
            .setSmallIcon(R.drawable.ic_notification)
            .setContentTitle(title)
            .setContentText(body)
            .setAutoCancel(true)
            .setSound(defaultSoundUri)
            .setContentIntent(pendingIntent)
            .setColor(getColor(R.color.primary_blue))
            .setPriority(NotificationCompat.PRIORITY_HIGH)
            .setDefaults(NotificationCompat.DEFAULT_ALL)
        
        // Add action buttons based on notification type
        val notificationType = data["type"]
        when (notificationType) {
            NotificationType.JOB_APPLICATION.name -> {
                // Add "View Application" action
                notificationBuilder.setStyle(
                    NotificationCompat.BigTextStyle().bigText(body)
                )
            }
            NotificationType.NEW_MESSAGE.name -> {
                // Add "Reply" action
                notificationBuilder.setStyle(
                    NotificationCompat.BigTextStyle().bigText(body)
                )
            }
            NotificationType.APPLICATION_STATUS_UPDATE.name -> {
                // Add "View Details" action
                notificationBuilder.setStyle(
                    NotificationCompat.BigTextStyle().bigText(body)
                )
            }
            NotificationType.NEW_JOB_MATCH.name -> {
                // Add "View Job" action
                notificationBuilder.setStyle(
                    NotificationCompat.BigTextStyle().bigText(body)
                )
            }
        }
        
        val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        
        // Generate unique notification ID
        val notificationId = System.currentTimeMillis().toInt()
        notificationManager.notify(notificationId, notificationBuilder.build())
    }
    
    private fun createNotificationIntent(data: Map<String, String>): Intent {
        val intent = Intent(this, SplashActivity::class.java)
        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
        
        // Add data to intent for deep linking
        data.forEach { (key, value) ->
            intent.putExtra(key, value)
        }
        
        return intent
    }
    
    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                CHANNEL_NAME,
                NotificationManager.IMPORTANCE_HIGH
            ).apply {
                description = CHANNEL_DESCRIPTION
                enableLights(true)
                lightColor = getColor(R.color.primary_blue)
                enableVibration(true)
                vibrationPattern = longArrayOf(0, 1000, 500, 1000)
                setShowBadge(true)
            }
            
            val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)
        }
    }
}
