package com.example.jobrec.services

import android.util.Log
import com.example.jobrec.BuildConfig
import com.example.jobrec.models.*
import com.google.auth.oauth2.GoogleCredentials
import com.google.firebase.firestore.FirebaseFirestore
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.tasks.await
import kotlinx.coroutines.withContext
import okhttp3.*
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.RequestBody.Companion.toRequestBody
import org.json.JSONObject
import java.io.ByteArrayInputStream
import java.io.IOException

class NotificationService private constructor() {
    
    companion object {
        private const val TAG = "NotificationService"
        private const val FCM_URL = "https://fcm.googleapis.com/v1/projects/careerworx-f5bc6/messages:send"
        private const val COLLECTION_NOTIFICATIONS = "notifications"
        
        // Service account key JSON - This should be stored securely
        // For production, use Firebase Functions or a secure backend
        private const val SERVICE_ACCOUNT_JSON = """
        *********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
        """
        
        @Volatile
        private var INSTANCE: NotificationService? = null
        
        fun getInstance(): NotificationService {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: NotificationService().also { INSTANCE = it }
            }
        }
    }
    
    private val db = FirebaseFirestore.getInstance()
    private val fcmTokenManager = FCMTokenManager.getInstance()
    private val client = OkHttpClient()
    
    suspend fun sendNotificationToUser(
        recipientId: String,
        title: String,
        body: String,
        type: NotificationType,
        data: Map<String, String> = emptyMap()
    ) {
        withContext(Dispatchers.IO) {
            try {
                // Get FCM tokens for the recipient
                val tokens = fcmTokenManager.getTokensForUser(recipientId)
                
                if (tokens.isEmpty()) {
                    Log.w(TAG, "No FCM tokens found for user: $recipientId")
                    return@withContext
                }
                
                // Create notification record
                val notification = PushNotification(
                    recipientId = recipientId,
                    title = title,
                    body = body,
                    type = type,
                    data = data
                )
                
                // Save notification to Firestore
                saveNotificationToFirestore(notification)
                
                // Send FCM message to each token
                tokens.forEach { token ->
                    sendFCMMessage(token.token, title, body, type, data)
                }
                
            } catch (e: Exception) {
                Log.e(TAG, "Error sending notification to user: $recipientId", e)
            }
        }
    }
    
    private suspend fun sendFCMMessage(
        token: String,
        title: String,
        body: String,
        type: NotificationType,
        data: Map<String, String>
    ) {
        try {
            val accessToken = getAccessToken()
            
            val messageData = mutableMapOf<String, String>().apply {
                putAll(data)
                put("type", type.name)
                put("title", title)
                put("body", body)
            }
            
            val fcmMessage = FCMMessage(
                message = FCMMessageContent(
                    token = token,
                    notification = FCMNotification(
                        title = title,
                        body = body
                    ),
                    data = messageData,
                    android = FCMAndroidConfig(
                        priority = "high",
                        notification = FCMAndroidNotification()
                    )
                )
            )
            
            val json = createFCMMessageJson(fcmMessage)
            
            val requestBody = json.toRequestBody("application/json".toMediaType())
            
            val request = Request.Builder()
                .url(FCM_URL)
                .post(requestBody)
                .addHeader("Authorization", "Bearer $accessToken")
                .addHeader("Content-Type", "application/json")
                .build()
            
            val response = client.newCall(request).execute()
            
            if (response.isSuccessful) {
                Log.d(TAG, "FCM message sent successfully")
            } else {
                Log.e(TAG, "Failed to send FCM message: ${response.code} - ${response.body?.string()}")
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "Error sending FCM message", e)
        }
    }
    
    private fun createFCMMessageJson(fcmMessage: FCMMessage): String {
        val json = JSONObject()
        val messageJson = JSONObject()
        
        messageJson.put("token", fcmMessage.message.token)
        
        // Notification
        val notificationJson = JSONObject()
        notificationJson.put("title", fcmMessage.message.notification.title)
        notificationJson.put("body", fcmMessage.message.notification.body)
        messageJson.put("notification", notificationJson)
        
        // Data
        val dataJson = JSONObject()
        fcmMessage.message.data.forEach { (key, value) ->
            dataJson.put(key, value)
        }
        messageJson.put("data", dataJson)
        
        // Android config
        val androidJson = JSONObject()
        androidJson.put("priority", "high")
        
        val androidNotificationJson = JSONObject()
        androidNotificationJson.put("icon", "ic_notification")
        androidNotificationJson.put("color", "#2196F3")
        androidNotificationJson.put("sound", "default")
        androidNotificationJson.put("channel_id", "careerworx_notifications")
        androidJson.put("notification", androidNotificationJson)
        
        messageJson.put("android", androidJson)
        json.put("message", messageJson)
        
        return json.toString()
    }
    
    private suspend fun getAccessToken(): String {
        return withContext(Dispatchers.IO) {
            try {
                // For production, use a proper service account key file
                // This is a simplified version for demonstration
                val credentials = GoogleCredentials.fromStream(
                    ByteArrayInputStream(SERVICE_ACCOUNT_JSON.toByteArray())
                ).createScoped(listOf("https://www.googleapis.com/auth/firebase.messaging"))
                
                credentials.refreshIfExpired()
                credentials.accessToken.tokenValue
            } catch (e: Exception) {
                Log.e(TAG, "Error getting access token", e)
                throw e
            }
        }
    }
    
    private suspend fun saveNotificationToFirestore(notification: PushNotification) {
        try {
            db.collection(COLLECTION_NOTIFICATIONS)
                .add(notification)
                .await()
            Log.d(TAG, "Notification saved to Firestore")
        } catch (e: Exception) {
            Log.e(TAG, "Error saving notification to Firestore", e)
        }
    }
}
