package com.example.jobrec.services

import android.util.Log
import com.example.jobrec.models.*
import com.google.firebase.firestore.FirebaseFirestore
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.tasks.await
import kotlinx.coroutines.withContext

class NotificationService private constructor() {

    companion object {
        private const val TAG = "NotificationService"
        private const val COLLECTION_NOTIFICATIONS = "notifications"

        @Volatile
        private var INSTANCE: NotificationService? = null

        fun getInstance(): NotificationService {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: NotificationService().also { INSTANCE = it }
            }
        }
    }

    private val db = FirebaseFirestore.getInstance()
    private val fcmTokenManager = FCMTokenManager.getInstance()

    suspend fun sendNotificationToUser(
        recipientId: String,
        title: String,
        body: String,
        type: NotificationType,
        data: Map<String, String> = emptyMap()
    ) {
        withContext(Dispatchers.IO) {
            try {
                // Create notification record
                val notification = PushNotification(
                    recipientId = recipientId,
                    title = title,
                    body = body,
                    type = type,
                    data = data
                )

                // Save notification to Firestore - Firebase Functions will handle sending
                saveNotificationToFirestore(notification)

                Log.d(TAG, "Notification queued for user: $recipientId")

            } catch (e: Exception) {
                Log.e(TAG, "Error sending notification to user: $recipientId", e)
            }
        }
    }



    private suspend fun saveNotificationToFirestore(notification: PushNotification) {
        try {
            // Save to notification queue - Firebase Functions will process this
            db.collection("notificationQueue")
                .add(notification)
                .await()
            Log.d(TAG, "Notification queued in Firestore")
        } catch (e: Exception) {
            Log.e(TAG, "Error saving notification to Firestore", e)
        }
    }

    suspend fun markNotificationAsRead(notificationId: String) {
        try {
            db.collection(COLLECTION_NOTIFICATIONS)
                .document(notificationId)
                .update("isRead", true)
                .await()

            Log.d(TAG, "Notification marked as read: $notificationId")
        } catch (e: Exception) {
            Log.e(TAG, "Error marking notification as read", e)
        }
    }

    suspend fun getUserNotifications(userId: String): List<PushNotification> {
        return try {
            val result = db.collection(COLLECTION_NOTIFICATIONS)
                .whereEqualTo("recipientId", userId)
                .orderBy("createdAt", com.google.firebase.firestore.Query.Direction.DESCENDING)
                .limit(50)
                .get()
                .await()

            result.documents.mapNotNull { doc ->
                doc.toObject(PushNotification::class.java)?.copy(id = doc.id)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting user notifications", e)
            emptyList()
        }
    }
}
