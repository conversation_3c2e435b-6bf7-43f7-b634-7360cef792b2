package com.example.jobrec.services

object OneSignalConfig {
    // You'll need to replace this with your actual OneSignal App ID
    // Get this from your OneSignal dashboard after creating an app
    const val ONESIGNAL_APP_ID = "YOUR_ONESIGNAL_APP_ID"
    
    // REST API Key for server-side notifications
    // Get this from OneSignal Settings > Keys & IDs
    const val ONESIGNAL_REST_API_KEY = "YOUR_ONESIGNAL_REST_API_KEY"
    
    // Notification categories/tags
    object Tags {
        const val USER_TYPE = "user_type"
        const val USER_ID = "user_id"
        const val COMPANY_ID = "company_id"
        const val FIELD = "field"
        const val SUBFIELD = "subfield"
        const val CITY = "city"
        const val PROVINCE = "province"
    }
    
    // Notification types for tracking
    object NotificationTypes {
        const val JOB_APPLICATION = "job_application"
        const val APPLICATION_STATUS = "application_status"
        const val NEW_MESSAGE = "new_message"
        const val JOB_MATCH = "job_match"
        const val INTERVIEW_SCHEDULED = "interview_scheduled"
    }
}
