package com.example.jobrec.services

object OneSignalConfig {
    // OneSignal App ID from your dashboard
    const val ONESIGNAL_APP_ID = "************************************"

    // REST API Key for server-side notifications
    const val ONESIGNAL_REST_API_KEY = "os_v2_app_sjhx4xvnfvfhnbgpmj546shjb4pos5l5y65e2cebmtrewziei2b4bb4r4rme37lp6jmw37dqdbzmfozae3saln5f7dvqcof2p4y2ypa"

    // Notification categories/tags
    object Tags {
        const val USER_TYPE = "user_type"
        const val USER_ID = "user_id"
        const val COMPANY_ID = "company_id"
        const val FIELD = "field"
        const val SUBFIELD = "subfield"
        const val CITY = "city"
        const val PROVINCE = "province"
    }

    // Notification types for tracking
    object NotificationTypes {
        const val JOB_APPLICATION = "job_application"
        const val APPLICATION_STATUS = "application_status"
        const val NEW_MESSAGE = "new_message"
        const val JOB_MATCH = "job_match"
        const val INTERVIEW_SCHEDULED = "interview_scheduled"
    }
}
