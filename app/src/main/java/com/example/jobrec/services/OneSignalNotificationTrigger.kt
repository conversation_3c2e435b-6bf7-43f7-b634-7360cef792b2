package com.example.jobrec.services

import android.util.Log
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class OneSignalNotificationTrigger private constructor() {
    
    companion object {
        private const val TAG = "OneSignalTrigger"
        
        @Volatile
        private var INSTANCE: OneSignalNotificationTrigger? = null
        
        fun getInstance(): OneSignalNotificationTrigger {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: OneSignalNotificationTrigger().also { INSTANCE = it }
            }
        }
    }
    
    private val oneSignalService = OneSignalService.getInstance()
    
    /**
     * Trigger notification when a student applies for a job
     */
    fun triggerJobApplicationNotification(
        companyUserId: String,
        applicantName: String,
        jobTitle: String,
        applicationId: String
    ) {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                val title = "New Job Application"
                val message = "$applicantName has applied for $jobTitle"
                val data = mapOf(
                    "applicationId" to applicationId,
                    "jobTitle" to jobTitle,
                    "applicantName" to applicant<PERSON><PERSON>,
                    "action" to "view_application"
                )
                
                oneSignalService.sendNotificationToUser(
                    userId = companyUserId,
                    title = title,
                    message = message,
                    data = data,
                    notificationType = OneSignalConfig.NotificationTypes.JOB_APPLICATION
                )
                
                Log.d(TAG, "Job application notification triggered for company user: $companyUserId")
            } catch (e: Exception) {
                Log.e(TAG, "Error triggering job application notification", e)
            }
        }
    }
    
    /**
     * Trigger notification when application status is updated
     */
    fun triggerApplicationStatusUpdateNotification(
        studentUserId: String,
        companyName: String,
        jobTitle: String,
        status: String,
        applicationId: String
    ) {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                val title = "Application Update"
                val message = when (status.lowercase()) {
                    "accepted" -> "🎉 Congratulations! Your application for $jobTitle at $companyName has been accepted"
                    "rejected" -> "Your application for $jobTitle at $companyName has been reviewed"
                    "reviewed" -> "📋 Your application for $jobTitle at $companyName is being reviewed"
                    "shortlisted" -> "⭐ Great news! You've been shortlisted for $jobTitle at $companyName"
                    "interviewing" -> "📅 You have an interview scheduled for $jobTitle at $companyName"
                    else -> "Your application for $jobTitle at $companyName has been updated"
                }
                
                val data = mapOf(
                    "applicationId" to applicationId,
                    "jobTitle" to jobTitle,
                    "companyName" to companyName,
                    "status" to status,
                    "action" to "view_application_details"
                )
                
                oneSignalService.sendNotificationToUser(
                    userId = studentUserId,
                    title = title,
                    message = message,
                    data = data,
                    notificationType = OneSignalConfig.NotificationTypes.APPLICATION_STATUS
                )
                
                Log.d(TAG, "Application status update notification triggered for student: $studentUserId")
            } catch (e: Exception) {
                Log.e(TAG, "Error triggering application status update notification", e)
            }
        }
    }
    
    /**
     * Trigger notification for new messages
     */
    fun triggerNewMessageNotification(
        recipientId: String,
        senderName: String,
        messagePreview: String,
        conversationId: String,
        senderType: String
    ) {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                val title = "💬 New Message from $senderName"
                val message = messagePreview.take(100).let { preview ->
                    if (messagePreview.length > 100) "$preview..." else preview
                }
                
                val data = mapOf(
                    "conversationId" to conversationId,
                    "senderName" to senderName,
                    "senderType" to senderType,
                    "action" to "open_chat"
                )
                
                oneSignalService.sendNotificationToUser(
                    userId = recipientId,
                    title = title,
                    message = message,
                    data = data,
                    notificationType = OneSignalConfig.NotificationTypes.NEW_MESSAGE
                )
                
                Log.d(TAG, "New message notification triggered for user: $recipientId")
            } catch (e: Exception) {
                Log.e(TAG, "Error triggering new message notification", e)
            }
        }
    }
    
    /**
     * Trigger notification for new job matches
     */
    fun triggerNewJobMatchNotification(
        jobTitle: String,
        companyName: String,
        jobField: String,
        jobSubField: String?,
        jobCity: String?,
        jobProvince: String?,
        matchPercentage: Int,
        jobId: String
    ) {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                val title = "🎯 New Job Match ($matchPercentage%)"
                val message = "New opportunity: $jobTitle at $companyName matches your profile!"
                
                val data = mapOf(
                    "jobId" to jobId,
                    "jobTitle" to jobTitle,
                    "companyName" to companyName,
                    "matchPercentage" to matchPercentage.toString(),
                    "action" to "view_job"
                )
                
                // Send to students in matching field/location
                oneSignalService.sendNotificationToMatchingStudents(
                    title = title,
                    message = message,
                    field = jobField,
                    subField = jobSubField,
                    city = jobCity,
                    province = jobProvince,
                    data = data,
                    notificationType = OneSignalConfig.NotificationTypes.JOB_MATCH
                )
                
                Log.d(TAG, "Job match notification triggered for job: $jobId")
            } catch (e: Exception) {
                Log.e(TAG, "Error triggering job match notification", e)
            }
        }
    }
    
    /**
     * Trigger notification for interview scheduling
     */
    fun triggerInterviewScheduledNotification(
        studentUserId: String,
        companyName: String,
        jobTitle: String,
        interviewDate: String,
        interviewTime: String,
        applicationId: String
    ) {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                val title = "📅 Interview Scheduled"
                val message = "You have an interview for $jobTitle at $companyName on $interviewDate at $interviewTime"
                
                val data = mapOf(
                    "applicationId" to applicationId,
                    "jobTitle" to jobTitle,
                    "companyName" to companyName,
                    "interviewDate" to interviewDate,
                    "interviewTime" to interviewTime,
                    "action" to "view_interview_details"
                )
                
                oneSignalService.sendNotificationToUser(
                    userId = studentUserId,
                    title = title,
                    message = message,
                    data = data,
                    notificationType = OneSignalConfig.NotificationTypes.INTERVIEW_SCHEDULED
                )
                
                Log.d(TAG, "Interview scheduled notification triggered for student: $studentUserId")
            } catch (e: Exception) {
                Log.e(TAG, "Error triggering interview scheduled notification", e)
            }
        }
    }
}
