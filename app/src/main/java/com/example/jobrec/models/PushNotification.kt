package com.example.jobrec.models

import com.google.firebase.Timestamp

data class PushNotification(
    val id: String = "",
    val recipientId: String = "",
    val senderId: String = "",
    val title: String = "",
    val body: String = "",
    val type: NotificationType = NotificationType.GENERAL,
    val data: Map<String, String> = emptyMap(),
    val isRead: Boolean = false,
    val createdAt: Timestamp = Timestamp.now(),
    val sentAt: Timestamp? = null
)

enum class NotificationType {
    JOB_APPLICATION,
    APPLICATION_STATUS_UPDATE,
    NEW_MESSAGE,
    NEW_JOB_MATCH,
    INTERVIEW_SCHEDULED,
    GENERAL
}

data class FCMMessage(
    val message: FCMMessageContent
)

data class FCMMessageContent(
    val token: String,
    val notification: FCMNotification,
    val data: Map<String, String> = emptyMap(),
    val android: FCMAndroidConfig? = null
)

data class FCMNotification(
    val title: String,
    val body: String,
    val image: String? = null
)

data class FCMAndroidConfig(
    val priority: String = "high",
    val notification: FCMAndroidNotification? = null
)

data class FCMAndroidNotification(
    val icon: String = "ic_notification",
    val color: String = "#2196F3",
    val sound: String = "default",
    val channelId: String = "careerworx_notifications"
)
