package com.example.jobrec

import android.util.Log
import com.google.firebase.messaging.FirebaseMessagingService
import com.google.firebase.messaging.RemoteMessage

class MyFirebaseMessagingService : FirebaseMessagingService() {

    override fun onNewToken(token: String) {
        super.onNewToken(token)
        Log.d("FCM_TOKEN", "New FCM token: $token")
        
        // This is where the FCM token should be generated
        // OneSignal should automatically pick this up
    }

    override fun onMessageReceived(remoteMessage: RemoteMessage) {
        super.onMessageReceived(remoteMessage)
        Log.d("FCM_MESSAGE", "Message received from: ${remoteMessage.from}")
        
        // Handle FCM messages here if needed
        remoteMessage.notification?.let {
            Log.d("FCM_MESSAGE", "Message Notification Body: ${it.body}")
        }
    }
}
