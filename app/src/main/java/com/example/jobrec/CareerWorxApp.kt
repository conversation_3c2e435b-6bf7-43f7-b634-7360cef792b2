package com.example.jobrec
import android.app.Application
import android.util.Log
import com.example.jobrec.services.OneSignalConfig
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.FirebaseFirestoreSettings
import com.google.firebase.messaging.FirebaseMessaging
import com.onesignal.OneSignal
import com.onesignal.debug.LogLevel
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
class CareerWorxApp : Application() {
    private val TAG = "CareerWorxApp"

    companion object {
        lateinit var instance: CareerWorxApp
            private set
    }

    override fun onCreate() {
        super.onCreate()
        instance = this

        // Initialize Firestore
        val settings = FirebaseFirestoreSettings.Builder()
            .setCacheSizeBytes(FirebaseFirestoreSettings.CACHE_SIZE_UNLIMITED)
            .build()
        FirebaseFirestore.getInstance().firestoreSettings = settings

        // Create Firestore indexes
        try {
            Log.d(TAG, "Creating Firestore indexes...")
            FirestoreIndexManager.createIndexes()
            Log.d(TAG, "Firestore indexes creation initiated")
        } catch (e: Exception) {
            Log.e(TAG, "Error creating Firestore indexes", e)
        }

        // Initialize FCM first
        initializeFirebaseMessaging()

        // Initialize OneSignal
        initializeOneSignal()
    }

    private fun initializeFirebaseMessaging() {
        try {
            Log.d(TAG, "Initializing Firebase Messaging...")

            // Get FCM token
            FirebaseMessaging.getInstance().token.addOnCompleteListener { task ->
                if (!task.isSuccessful) {
                    Log.w(TAG, "Fetching FCM registration token failed", task.exception)
                    return@addOnCompleteListener
                }

                // Get new FCM registration token
                val token = task.result
                Log.d(TAG, "FCM Registration Token: $token")

                if (token.isNullOrEmpty()) {
                    Log.e(TAG, "FCM token is null or empty!")
                } else {
                    Log.d(TAG, "FCM token generated successfully: ${token.take(20)}...")
                }
            }

        } catch (e: Exception) {
            Log.e(TAG, "Error initializing Firebase Messaging", e)
        }
    }

    private fun initializeOneSignal() {
        try {
            Log.d(TAG, "Starting OneSignal initialization...")

            // Verbose Logging set to help debug issues, remove before releasing your app.
            OneSignal.Debug.logLevel = LogLevel.VERBOSE

            // OneSignal Initialization
            OneSignal.initWithContext(this, OneSignalConfig.ONESIGNAL_APP_ID)
            Log.d(TAG, "OneSignal initWithContext called with App ID: ${OneSignalConfig.ONESIGNAL_APP_ID}")

            // Log basic OneSignal info (simplified without observers)
            Log.d(TAG, "OneSignal SDK initialized - check dashboard for user registration")

            // Request notification permission in a coroutine
            CoroutineScope(Dispatchers.Main).launch {
                try {
                    Log.d(TAG, "Requesting OneSignal notification permission...")
                    val accepted = OneSignal.Notifications.requestPermission(false)
                    Log.d(TAG, "OneSignal notification permission result: $accepted")

                    // Check permission status
                    val hasPermission = OneSignal.Notifications.permission
                    Log.d(TAG, "Current notification permission: $hasPermission")

                } catch (e: Exception) {
                    Log.e(TAG, "Error requesting OneSignal permission", e)
                }
            }

            Log.d(TAG, "OneSignal initialized successfully")
        } catch (e: Exception) {
            Log.e(TAG, "Error initializing OneSignal", e)
        }
    }
}
