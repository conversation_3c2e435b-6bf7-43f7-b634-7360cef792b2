package com.example.jobrec
import android.app.Application
import android.util.Log
import com.example.jobrec.services.OneSignalConfig
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.FirebaseFirestoreSettings
import com.onesignal.OneSignal
import com.onesignal.debug.LogLevel
class CareerWorxApp : Application() {
    private val TAG = "CareerWorxApp"

    companion object {
        lateinit var instance: CareerWorxApp
            private set
    }

    override fun onCreate() {
        super.onCreate()
        instance = this

        // Initialize Firestore
        val settings = FirebaseFirestoreSettings.Builder()
            .setCacheSizeBytes(FirebaseFirestoreSettings.CACHE_SIZE_UNLIMITED)
            .build()
        FirebaseFirestore.getInstance().firestoreSettings = settings

        // Create Firestore indexes
        try {
            Log.d(TAG, "Creating Firestore indexes...")
            FirestoreIndexManager.createIndexes()
            Log.d(TAG, "Firestore indexes creation initiated")
        } catch (e: Exception) {
            Log.e(TAG, "Error creating Firestore indexes", e)
        }

        // Initialize OneSignal
        initializeOneSignal()
    }

    private fun initializeOneSignal() {
        try {
            // Verbose Logging set to help debug issues, remove before releasing your app.
            OneSignal.Debug.logLevel = LogLevel.VERBOSE

            // OneSignal Initialization
            OneSignal.initWithContext(this, OneSignalConfig.ONESIGNAL_APP_ID)

            // Request notification permission (simplified call)
            OneSignal.Notifications.requestPermission(false)

            Log.d(TAG, "OneSignal initialized successfully")
        } catch (e: Exception) {
            Log.e(TAG, "Error initializing OneSignal", e)
        }
    }
}
