package com.example.jobrec
import android.app.Application
import android.app.NotificationChannel
import android.app.NotificationManager
import android.content.Context
import android.os.Build
import android.util.Log
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.FirebaseFirestoreSettings
class CareerWorxApp : Application() {
    private val TAG = "CareerWorxApp"

    companion object {
        lateinit var instance: CareerWorxApp
            private set
    }

    override fun onCreate() {
        super.onCreate()
        instance = this

        // Initialize Firestore
        val settings = FirebaseFirestoreSettings.Builder()
            .setCacheSizeBytes(FirebaseFirestoreSettings.CACHE_SIZE_UNLIMITED)
            .build()
        FirebaseFirestore.getInstance().firestoreSettings = settings

        // Create Firestore indexes
        try {
            Log.d(TAG, "Creating Firestore indexes...")
            FirestoreIndexManager.createIndexes()
            Log.d(TAG, "Firestore indexes creation initiated")
        } catch (e: Exception) {
            Log.e(TAG, "Error creating Firestore indexes", e)
        }

        // Initialize notification channels
        createNotificationChannels()
    }

    private fun createNotificationChannels() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager

            // Main notification channel
            val mainChannel = NotificationChannel(
                "careerworx_notifications",
                "CareerWorx Notifications",
                NotificationManager.IMPORTANCE_HIGH
            ).apply {
                description = "Notifications for job applications, messages, and updates"
                enableLights(true)
                lightColor = getColor(R.color.primary_blue)
                enableVibration(true)
                vibrationPattern = longArrayOf(0, 1000, 500, 1000)
                setShowBadge(true)
            }

            notificationManager.createNotificationChannel(mainChannel)
            Log.d(TAG, "Notification channels created")
        }
    }
}
