package com.example.jobrec.utils

import android.util.Log
import com.example.jobrec.services.OneSignalConfig
import com.example.jobrec.services.OneSignalNotificationTrigger
import com.example.jobrec.services.OneSignalService
import com.google.firebase.auth.FirebaseAuth
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

object OneSignalTestUtils {
    
    private const val TAG = "OneSignalTest"
    
    /**
     * Test function to send a sample notification to the current user
     * Call this from any activity to test the notification system
     */
    fun sendTestNotification() {
        val currentUser = FirebaseAuth.getInstance().currentUser
        if (currentUser == null) {
            Log.w(TAG, "No user logged in, cannot send test notification")
            return
        }
        
        CoroutineScope(Dispatchers.IO).launch {
            try {
                OneSignalService.getInstance().sendNotificationToUser(
                    userId = currentUser.uid,
                    title = "🧪 Test Notification",
                    message = "This is a test notification to verify <PERSON><PERSON>ignal is working correctly!",
                    data = mapOf(
                        "test" to "true",
                        "action" to "test_notification"
                    ),
                    notificationType = OneSignalConfig.NotificationTypes.JOB_APPLICATION
                )
                Log.d(TAG, "Test notification sent successfully")
            } catch (e: Exception) {
                Log.e(TAG, "Error sending test notification", e)
            }
        }
    }
    
    /**
     * Test function to send a job application notification
     */
    fun sendTestJobApplicationNotification(companyUserId: String) {
        OneSignalNotificationTrigger.getInstance().triggerJobApplicationNotification(
            companyUserId = companyUserId,
            applicantName = "Test Student",
            jobTitle = "Software Developer",
            applicationId = "test_app_123"
        )
        Log.d(TAG, "Test job application notification sent")
    }
    
    /**
     * Test function to send an application status update notification
     */
    fun sendTestStatusUpdateNotification(studentUserId: String) {
        OneSignalNotificationTrigger.getInstance().triggerApplicationStatusUpdateNotification(
            studentUserId = studentUserId,
            companyName = "Test Company",
            jobTitle = "Software Developer",
            status = "accepted",
            applicationId = "test_app_123"
        )
        Log.d(TAG, "Test status update notification sent")
    }
    
    /**
     * Test function to send a message notification
     */
    fun sendTestMessageNotification(recipientUserId: String) {
        OneSignalNotificationTrigger.getInstance().triggerNewMessageNotification(
            recipientId = recipientUserId,
            senderName = "Test Sender",
            messagePreview = "This is a test message to verify notifications are working!",
            conversationId = "test_conversation",
            senderType = "company"
        )
        Log.d(TAG, "Test message notification sent")
    }
    
    /**
     * Test function to send a job match notification
     */
    fun sendTestJobMatchNotification() {
        OneSignalNotificationTrigger.getInstance().triggerNewJobMatchNotification(
            jobTitle = "Test Job Position",
            companyName = "Test Company",
            jobField = "Technology",
            jobSubField = "Software Development",
            jobCity = "Cape Town",
            jobProvince = "Western Cape",
            matchPercentage = 95,
            jobId = "test_job_123"
        )
        Log.d(TAG, "Test job match notification sent")
    }
    
    /**
     * Test function to send to all students
     */
    fun sendTestNotificationToAllStudents() {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                OneSignalService.getInstance().sendNotificationToMatchingStudents(
                    title = "📢 Test Announcement",
                    message = "This is a test notification sent to all students!",
                    data = mapOf("test" to "true"),
                    notificationType = "test"
                )
                Log.d(TAG, "Test notification sent to all students")
            } catch (e: Exception) {
                Log.e(TAG, "Error sending test notification to students", e)
            }
        }
    }
    
    /**
     * Test function to send to all companies
     */
    fun sendTestNotificationToAllCompanies() {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                OneSignalService.getInstance().sendNotificationToCompanies(
                    title = "📢 Test Company Announcement",
                    message = "This is a test notification sent to all companies!",
                    data = mapOf("test" to "true"),
                    notificationType = "test"
                )
                Log.d(TAG, "Test notification sent to all companies")
            } catch (e: Exception) {
                Log.e(TAG, "Error sending test notification to companies", e)
            }
        }
    }
}
