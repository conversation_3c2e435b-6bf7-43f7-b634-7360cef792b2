package com.example.jobrec.utils

import android.util.Log
import com.example.jobrec.services.OneSignalConfig
import com.example.jobrec.services.OneSignalNotificationTrigger
import com.example.jobrec.services.OneSignalService
import com.google.firebase.auth.FirebaseAuth
import com.onesignal.OneSignal
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.RequestBody.Companion.toRequestBody

object OneSignalTestUtils {

    private const val TAG = "OneSignalTest"

    /**
     * Test function to send a sample notification to ALL users
     * This is easier to test than targeting specific users
     */
    fun sendTestNotificationToAll() {
        Log.d(TAG, "Sending test notification to all users...")

        CoroutineScope(Dispatchers.IO).launch {
            try {
                // First check if we have any subscribed users
                Log.d(TAG, "Checking OneSignal subscription status...")
                val isSubscribed = OneSignal.User.pushSubscription.optedIn
                val pushToken = OneSignal.User.pushSubscription.token
                val oneSignalId = OneSignal.User.onesignalId

                Log.d(TAG, "Current User Status:")
                Log.d(TAG, "  - OneSignal ID: $oneSignalId")
                Log.d(TAG, "  - Push Token: $pushToken")
                Log.d(TAG, "  - Subscribed: $isSubscribed")

                if (!isSubscribed) {
                    Log.w(TAG, "User is not subscribed to push notifications!")
                    Log.w(TAG, "Make sure OneSignal platform is configured in dashboard")
                    return@launch
                }

                // Send to all subscribed users using OneSignal REST API
                val notification = org.json.JSONObject().apply {
                    put("app_id", OneSignalConfig.ONESIGNAL_APP_ID)
                    put("headings", org.json.JSONObject().put("en", "🧪 Test Notification"))
                    put("contents", org.json.JSONObject().put("en", "This is a test notification from CareerWorx app!"))
                    put("included_segments", org.json.JSONArray().put("Subscribed Users"))

                    // Add custom data
                    put("data", org.json.JSONObject().apply {
                        put("type", "test")
                        put("action", "test_notification")
                    })
                }

                val client = okhttp3.OkHttpClient()
                val requestBody = notification.toString()
                    .toRequestBody("application/json".toMediaType())

                val request = okhttp3.Request.Builder()
                    .url("https://onesignal.com/api/v1/notifications")
                    .post(requestBody)
                    .addHeader("Authorization", "Basic ${OneSignalConfig.ONESIGNAL_REST_API_KEY}")
                    .addHeader("Content-Type", "application/json")
                    .build()

                Log.d(TAG, "Sending notification with payload: ${notification.toString()}")

                client.newCall(request).execute().use { response ->
                    val responseBody = response.body?.string()
                    if (response.isSuccessful) {
                        Log.d(TAG, "Test notification sent successfully!")
                        Log.d(TAG, "Response: $responseBody")
                    } else {
                        Log.e(TAG, "Failed to send test notification: ${response.code}")
                        Log.e(TAG, "Error body: $responseBody")

                        // Parse error for better debugging
                        responseBody?.let { body ->
                            if (body.contains("not subscribed")) {
                                Log.e(TAG, "ERROR: No subscribed users found!")
                                Log.e(TAG, "SOLUTION: Configure Android platform in OneSignal dashboard")
                            }
                        }
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error sending test notification", e)
            }
        }
    }

    /**
     * Test function to send a job application notification
     */
    fun sendTestJobApplicationNotification(companyUserId: String) {
        OneSignalNotificationTrigger.getInstance().triggerJobApplicationNotification(
            companyUserId = companyUserId,
            applicantName = "Test Student",
            jobTitle = "Software Developer",
            applicationId = "test_app_123"
        )
        Log.d(TAG, "Test job application notification sent")
    }

    /**
     * Test function to send an application status update notification
     */
    fun sendTestStatusUpdateNotification(studentUserId: String) {
        OneSignalNotificationTrigger.getInstance().triggerApplicationStatusUpdateNotification(
            studentUserId = studentUserId,
            companyName = "Test Company",
            jobTitle = "Software Developer",
            status = "accepted",
            applicationId = "test_app_123"
        )
        Log.d(TAG, "Test status update notification sent")
    }

    /**
     * Test function to send a message notification
     */
    fun sendTestMessageNotification(recipientUserId: String) {
        OneSignalNotificationTrigger.getInstance().triggerNewMessageNotification(
            recipientId = recipientUserId,
            senderName = "Test Sender",
            messagePreview = "This is a test message to verify notifications are working!",
            conversationId = "test_conversation",
            senderType = "company"
        )
        Log.d(TAG, "Test message notification sent")
    }

    /**
     * Test function to send a job match notification
     */
    fun sendTestJobMatchNotification() {
        OneSignalNotificationTrigger.getInstance().triggerNewJobMatchNotification(
            jobTitle = "Test Job Position",
            companyName = "Test Company",
            jobField = "Technology",
            jobSubField = "Software Development",
            jobCity = "Cape Town",
            jobProvince = "Western Cape",
            matchPercentage = 95,
            jobId = "test_job_123"
        )
        Log.d(TAG, "Test job match notification sent")
    }

    /**
     * Test function to send to all students
     */
    fun sendTestNotificationToAllStudents() {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                OneSignalService.getInstance().sendNotificationToMatchingStudents(
                    title = "📢 Test Announcement",
                    message = "This is a test notification sent to all students!",
                    data = mapOf("test" to "true"),
                    notificationType = "test"
                )
                Log.d(TAG, "Test notification sent to all students")
            } catch (e: Exception) {
                Log.e(TAG, "Error sending test notification to students", e)
            }
        }
    }

    /**
     * Test function to send to all companies
     */
    fun sendTestNotificationToAllCompanies() {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                OneSignalService.getInstance().sendNotificationToCompanies(
                    title = "📢 Test Company Announcement",
                    message = "This is a test notification sent to all companies!",
                    data = mapOf("test" to "true"),
                    notificationType = "test"
                )
                Log.d(TAG, "Test notification sent to all companies")
            } catch (e: Exception) {
                Log.e(TAG, "Error sending test notification to companies", e)
            }
        }
    }
}
