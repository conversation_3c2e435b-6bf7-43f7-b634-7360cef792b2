package com.example.jobrec.utils

import android.util.Log
import com.example.jobrec.models.NotificationType
import com.example.jobrec.services.NotificationTriggerService
import com.google.firebase.auth.FirebaseAuth
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

object NotificationTestUtils {
    
    private const val TAG = "NotificationTest"
    
    /**
     * Test function to send a sample notification to the current user
     * This can be called from any activity to test the notification system
     */
    fun sendTestNotification() {
        val currentUser = FirebaseAuth.getInstance().currentUser
        if (currentUser == null) {
            Log.w(TAG, "No user logged in, cannot send test notification")
            return
        }
        
        CoroutineScope(Dispatchers.IO).launch {
            try {
                NotificationTriggerService.getInstance().triggerNewMessageNotification(
                    recipientId = currentUser.uid,
                    senderName = "Test Sender",
                    messagePreview = "This is a test notification to verify the push notification system is working correctly.",
                    conversationId = "test_conversation",
                    senderType = "system"
                )
                Log.d(TAG, "Test notification sent successfully")
            } catch (e: Exception) {
                Log.e(TAG, "Error sending test notification", e)
            }
        }
    }
    
    /**
     * Test function to send a job application notification
     */
    fun sendTestJobApplicationNotification(companyUserId: String) {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                NotificationTriggerService.getInstance().triggerJobApplicationNotification(
                    companyUserId = companyUserId,
                    applicantName = "Test Applicant",
                    jobTitle = "Test Job Position",
                    applicationId = "test_application_123"
                )
                Log.d(TAG, "Test job application notification sent")
            } catch (e: Exception) {
                Log.e(TAG, "Error sending test job application notification", e)
            }
        }
    }
    
    /**
     * Test function to send an application status update notification
     */
    fun sendTestStatusUpdateNotification(studentUserId: String) {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                NotificationTriggerService.getInstance().triggerApplicationStatusUpdateNotification(
                    studentUserId = studentUserId,
                    companyName = "Test Company",
                    jobTitle = "Test Job Position",
                    status = "accepted",
                    applicationId = "test_application_123"
                )
                Log.d(TAG, "Test status update notification sent")
            } catch (e: Exception) {
                Log.e(TAG, "Error sending test status update notification", e)
            }
        }
    }
    
    /**
     * Test function to send a job match notification
     */
    fun sendTestJobMatchNotification(studentUserId: String) {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                NotificationTriggerService.getInstance().triggerNewJobMatchNotification(
                    studentUserId = studentUserId,
                    jobTitle = "Test Job Match",
                    companyName = "Test Company",
                    matchPercentage = 85,
                    jobId = "test_job_123"
                )
                Log.d(TAG, "Test job match notification sent")
            } catch (e: Exception) {
                Log.e(TAG, "Error sending test job match notification", e)
            }
        }
    }
}
